﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Sockets;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using HolyBless.Configs;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.VirtualFolders;
using HolyBless.Enums;
using HolyBless.Helpers;
using HolyBless.Repositories;
using HolyBless.StorageProviders.Dtos;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace HolyBless.StorageProviders
{
    public class S3StorageManager : ApplicationService, IS3StorageManager
    {
        private readonly IRepository<StorageBucket> _bucketRepository;
        private readonly IRepository<ProviderSecret> _providerSecretRepository;
        private readonly IBucketFileRepository _bucketFileRepository;
        private readonly IRepository<BucketFileUrl> _bucketFileUrlRepository;
        private readonly IRepository<VirtualDiskFolder> _virtualFolderRepository;
        private readonly IRepository<FolderToFile> _folderToFileRepository;
        private readonly IStorageProviderAppService _storageProviderAppService;
        private readonly IUnitOfWorkManager _unitOfWorkManager;
        private readonly AppConfig _settings;

        public S3StorageManager
            (
            IRepository<StorageBucket> bucketRepository
            , IRepository<ProviderSecret> providerSecretRepository
            , IBucketFileRepository bucketFileRepository
            , IRepository<BucketFileUrl> bucketFileUrlRepository
            , IRepository<VirtualDiskFolder> virtualFolderRepository
            , IRepository<FolderToFile> folderToFileRepository
            , IStorageProviderAppService storageProviderAppService
            , IUnitOfWorkManager unitOfWorkManager
            , IOptions<AppConfig> settings
            )
        {
            _bucketRepository = bucketRepository;
            _providerSecretRepository = providerSecretRepository;
            _bucketFileRepository = bucketFileRepository;
            _bucketFileUrlRepository = bucketFileUrlRepository;
            _virtualFolderRepository = virtualFolderRepository;
            _folderToFileRepository = folderToFileRepository;
            _storageProviderAppService = storageProviderAppService;
            _unitOfWorkManager = unitOfWorkManager;
            _settings = settings.Value;
        }

        private async Task<List<string>> ListBucketsAsync(ProviderSecret providerSecret)
        {
            return await ListBucketsAsync(providerSecret.AccessId, providerSecret.AccessSecretKey, providerSecret.ApiEndPoint);
        }

        private async Task<List<string>> ListBucketsAsync(string accessId, string accessSecretKey, string apiEndPoint)
        {
            var bucketNames = new List<string>();
            try
            {
                using var s3Client = GetS3Client(accessId, accessSecretKey, apiEndPoint);
                var response = await s3Client.ListBucketsAsync();
                foreach (var bucket in response.Buckets)
                {
                    bucketNames.Add(bucket.BucketName);
                }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }

            return bucketNames;
        }

        private async Task<StorageBucket> GetBucketById(int bucketId)
        {
            var query = await _bucketRepository.GetQueryableAsync();
            var bucket = await query.Include(x => x.StorageProvider).FirstOrDefaultAsync(x => x.Id == bucketId);
            Check.NotNull(bucket, nameof(StorageBucket));
            return bucket;
        }

        private AmazonS3Client GetS3Client(string accessId, string accessSecretKey, string apiEndPoint)
        {
            var config = new AmazonS3Config
            {
                ServiceURL = apiEndPoint,
                ForcePathStyle = false // AliYun requires path-style addressing to false
            };

            if (apiEndPoint.Contains(".r2.", StringComparison.OrdinalIgnoreCase))
            {
                config.ForcePathStyle = true; //R2 requires path-style addressing to true
            }
            var s3Client = new AmazonS3Client(accessId, accessSecretKey, config);
            return s3Client;
        }

        [HttpGet]
        public async Task<List<string>> ListSubfoldersAsync(int bucketId, string prefix = "")
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            var subfolders = new List<string>();

            try
            {
                var bucket = await GetBucketById(bucketId);
                var provider = bucket.StorageProvider;
                var request = new ListObjectsV2Request
                {
                    BucketName = bucket.BucketName,
                    Prefix = prefix,         // Start from a given prefix
                    Delimiter = "/"          // Treat `/` as a folder separator
                };

                // These properties are no longer available in StorageProvider class
                // var s3Client = GetS3Client(provider.AccessId, provider.AccessSecretKey, provider.ApiEndPoint);
                // var response = await s3Client.ListObjectsV2Async(request);

                throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");

                // Fetch common prefixes (subfolders)
                // subfolders.AddRange(response.CommonPrefixes);

                // Optionally, print objects (files) at the current level
                // foreach (var obj in response.S3Objects)
                // {
                //     Console.WriteLine($"File: {obj.Key}");
                // }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }

            return subfolders;
        }

        [HttpGet]
        public async Task<List<string>> ListFilesAsync(int bucketId)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");
        }

        [HttpGet]
        [Route("DownloadBucketFile")]
        public async Task<byte[]> DownloadBucketFile(int bucketId, string fileNamePath)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            // MemoryStream memory = await DownloadFileAsync(bucketId, fileNamePath);
            // memory.Position = 0;

            // var contentType = "application/octet-stream"; // Change based on file type if needed
            // var fileName = Path.GetFileName(fileNamePath);
            // return new FileStreamResult(memory, contentType)
            // {
            //     FileDownloadName = fileName
            // };

            throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");
        }

        public async Task<bool> UploadFileAsync(int bucketId, string subFolder, string fileName, Stream fileStream)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            try
            {
                var bucket = await GetBucketById(bucketId);
                var provider = bucket.StorageProvider;
                // Combine subfolder and filename to create the object key
                var objectKey = $"{subFolder.TrimEnd('/')}/{Path.GetFileName(fileName)}";

                var request = new PutObjectRequest
                {
                    BucketName = bucket.BucketName,
                    Key = objectKey,
                    InputStream = fileStream,
                    ContentType = "application/octet-stream" // Set appropriate MIME type if known
                };

                // These properties are no longer available in StorageProvider class
                // var s3Client = GetS3Client(provider.AccessId, provider.AccessSecretKey, provider.ApiEndPoint);
                // var response = await s3Client.PutObjectAsync(request);

                throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");

                // if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                // {
                //     return true;
                // }
                // else
                // {
                //     Logger.LogError("Failed to upload file. StatusCode: {StatusCode}", response.HttpStatusCode);
                //     return false;
                // }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<MemoryStream> DownloadFileAsync(int bucketId, string fieKey)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            try
            {
                var bucket = await GetBucketById(bucketId);
                var provider = bucket.StorageProvider;
                var request = new GetObjectRequest
                {
                    BucketName = bucket.BucketName,
                    Key = fieKey
                };

                // These properties are no longer available in StorageProvider class
                // var s3Client = GetS3Client(provider.AccessId, provider.AccessSecretKey, provider.ApiEndPoint);
                // using (var response = await s3Client.GetObjectAsync(request))
                // {
                //     var memoryStream = new MemoryStream();
                //     await response.ResponseStream.CopyToAsync(memoryStream);
                //     return memoryStream;
                // }

                throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }
        }

        /// <summary>
        /// Walks through all CloudFlare storage buckets and their files, updating the BucketFile table
        /// </summary>
        /// <returns>Number of files processed</returns>
        [HttpPost]
        public async Task<int> IndexCloudFlareFilesAsync()
        {
            var filesProcessed = 0;

            try
            {
                var processingStartTime = DateTime.UtcNow;
                await ResetExistFlag(); // Reset all existing files to not exist before processing

                // Get provider secret (assuming there's one secret for CloudFlare)
                var providerSecret = await GetProviderSecretAsync();
                var actualCfBuckets = await ListBucketsAsync(providerSecret);

                // Get all distinct StorageBuckets for CloudFlare providers matching current environment
                var cfBuckets = await GetCloudFlareStorageBucketsAsync();

                if (!cfBuckets.Any())
                {
                    Logger.LogWarning("No CloudFlare storage buckets found for environment: {Environment}", _settings.Environment);
                    return 0;
                }
                var distinctBuckets = cfBuckets.Where(x => actualCfBuckets.Contains(x.BucketName)).Select(x => new BucketStorageMeta
                {
                    BucketName = x.BucketName,
                    LanguageCode = x.LanguageCode,
                    SpokenLangCode = x.SpokenLangCode,
                    ContentType = x.ContentType,
                    SubDomain = x.SubDomain,
                    Domain = x.StorageProvider.BindedDomain ?? string.Empty,
                }).Distinct().ToList();

                // Process each bucket
                var groupedBuckets = distinctBuckets
                                   .GroupBy(bucket => bucket.BucketName)
                                   .ToList();
                foreach (var grp in groupedBuckets)
                {
                    using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);
                    var bucketName = grp.Key;
                    //Not sure why above distinct doesn't remove duplicate
                    var groupItems = new List<BucketStorageMeta> { grp.First() };
                    var bucketFilesProcessed = await ProcessBucketFilesAsync(bucketName, groupItems, providerSecret, ProviderCodeConstants.CloudFlare);
                    filesProcessed += bucketFilesProcessed;
                    await uow.SaveChangesAsync();
                    await uow.CompleteAsync();
                }
                //await UpdateNotExistFiles(processingStartTime);
                Logger.LogInformation("Total files processed: {TotalFiles}", filesProcessed);
                return filesProcessed;
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }
        }

        private async Task ResetExistFlag()
        {
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);
            // Reset all BucketFile Exists flag to false for the current environment
            await _bucketFileRepository.UpdateExistFlagAsync(false);
            await uow.CompleteAsync();
        }

        private async Task UpdateNotExistFiles(DateTime processTime)
        {
            var notExists = await _bucketFileRepository.GetListAsync(x => x.Environment == _settings.Environment &&
                    x.LastModificationTime < processTime);
            foreach (var file in notExists)
            {
                file.Exists = false;
            }
            await _bucketFileRepository.UpdateManyAsync(notExists);
        }

        /// <summary>
        /// Gets CloudFlare storage buckets using the optimized cached provider service.
        /// This method leverages StorageProviderAppService's GetCachedAllProviders for better performance.
        /// </summary>
        /// <returns>List of CloudFlare storage buckets with StorageProvider included</returns>
        private async Task<List<StorageBucket>> GetCloudFlareStorageBucketsAsync()
        {
            try
            {
                // Use the optimized cached bucket DTOs from StorageProviderAppService
                var bucketDtos = await _storageProviderAppService.GetCloudFlareBucketsAsync();

                if (!bucketDtos.Any())
                {
                    Logger.LogWarning("No CloudFlare buckets found via StorageProviderAppService for environment: {Environment}",
                        _settings.Environment);
                    return new List<StorageBucket>();
                }

                //Only check cloud flare bucket
                var bucketIds = bucketDtos.Select(dto => dto.Id).ToList();
                var query = await _bucketRepository.GetQueryableAsync();

                var buckets = await query
                    .Include(x => x.StorageProvider) // Required for CloudFlare operations
                    .Where(x => bucketIds.Contains(x.Id))
                    .OrderBy(x => x.BucketName) // Consistent ordering
                    .ToListAsync();

                Logger.LogDebug("Retrieved {BucketCount} CloudFlare buckets from repository for synchronization",
                    buckets.Count);

                return buckets;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving CloudFlare buckets for file synchronization");
                throw;
            }
        }

        private async Task<ProviderSecret> GetProviderSecretAsync()
        {
            // For now, get the first available provider secret
            // In a real scenario, you might want to link secrets to specific providers
            var secret = await _providerSecretRepository.FirstOrDefaultAsync();
            Check.NotNull(secret, nameof(ProviderSecret), "No provider secret found. Please configure CloudFlare credentials.");
            return secret;
        }

        private async Task<int> ProcessBucketFilesAsync(string bucketName, List<BucketStorageMeta> buckets, ProviderSecret secret, string providerCode)
        {
            var filesProcessed = 0;

            try
            {
                using var s3Client = GetS3Client(secret.AccessId, secret.AccessSecretKey, secret.ApiEndPoint);

                // List all objects in the bucket
                var request = new ListObjectsV2Request
                {
                    BucketName = bucketName,
                    MaxKeys = 1000 // Process in batches
                };

                ListObjectsV2Response response;
                do
                {
                    response = await s3Client.ListObjectsV2Async(request);

                    foreach (var s3Object in response.S3Objects)
                    {
                        // Skip folders (objects ending with /)
                        if (s3Object.Key.EndsWith('/'))
                            continue;

                        await ProcessSingleFileAsync(bucketName, buckets, s3Object, providerCode);
                        filesProcessed++;
                    }

                    // Set continuation token for next batch
                    request.ContinuationToken = response.NextContinuationToken;
                } while (response.IsTruncated);

                return filesProcessed;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing files for bucket: {BucketName}", bucketName);
                throw;
            }
        }

        private async Task ProcessSingleFileAsync(string bucketName, List<BucketStorageMeta> buckets, S3Object s3Object, string providerCode)
        {
            try
            {
                // Extract file name and relative path from CloudFlare S3 object key
                var (fileName, relativePath) = ExtractFileNameAndPath(s3Object.Key);
                if (s3Object.Key.StartsWith(".recycle") || s3Object.Key.EndsWith("index.html", StringComparison.InvariantCultureIgnoreCase))
                {
                    return;
                }
                foreach (var bucket in buckets)
                {
                    // Check if file already exists in database
                    var existingFiles = await _bucketFileRepository.GetListAsync(f =>
                        f.FileName == fileName &&
                        (f.RelativePathInBucket ?? "") == (relativePath ?? "") &&
                         (string.IsNullOrWhiteSpace(f.BucketName) && f.Size == s3Object.Size ||
                         !string.IsNullOrWhiteSpace(f.BucketName) && (f.BucketName ?? "") == (bucketName ?? "")) &&
                         f.Environment == _settings.Environment)
                        ;
                    var existingFile = existingFiles.OrderBy(x => x.Id).FirstOrDefault();
                    /*if (existingFiles.Count > 1)
                    {
                        await _bucketFileRepository.DeleteManyAsync(existingFiles.Skip(1), true); // Remove duplicates, keep the first one
                    }*/
                    if (existingFile != null)
                    {
                        // Update existing file with CloudFlare information
                        existingFile.Exists = true;
                        existingFile.LastModificationTime = s3Object.LastModified;

                        // Ensure FileName and RelativePathInBucket are correctly set from CloudFlare
                        existingFile.FileName = fileName;
                        existingFile.RelativePathInBucket = relativePath;
                        existingFile.LanguageCode = bucket.LanguageCode;
                        existingFile.SpokenLangCode = bucket.SpokenLangCode;
                        existingFile.ContentCategory = bucket.ContentType;
                        existingFile.Size = s3Object.Size;
                        existingFile.BucketName = bucketName;
                        existingFile.MediaType = MediaTypeHelper.GetMediaType(fileName);
                        await _bucketFileRepository.UpdateAsync(existingFile, true);

                        // Create or update BucketFileUrl record for CloudFlare
                        //var computeUrl = CombineUrl(bucket.Domain, bucket.SubDomain, relativePath, fileName);
                        //await InsertOrUpdateFileUrl(existingFile.Id, computeUrl, providerCode);

                        Logger.LogDebug("Updated existing BucketFile: {FileName} in path: {RelativePath}",
                            fileName, relativePath);
                    }
                    else
                    {
                        // Create new file record with properly extracted CloudFlare path information
                        var bucketFile = new BucketFile
                        {
                            FileName = fileName,
                            RelativePathInBucket = relativePath,
                            LanguageCode = bucket.LanguageCode,
                            SpokenLangCode = bucket.SpokenLangCode,
                            Environment = _settings.Environment,
                            Exists = true,
                            Size = s3Object.Size,
                            BucketName = bucketName,
                            MediaType = MediaTypeHelper.GetMediaType(fileName),
                            ContentCategory = bucket.ContentType,
                            Views = 0
                        };

                        // Set LastModificationTime from S3 object
                        bucketFile.LastModifiedAtStorage = s3Object.LastModified;

                        bucketFile = await _bucketFileRepository.InsertAsync(bucketFile, true);

                        // Create BucketFileUrl record for CloudFlare
                        //var computeUrl = CombineUrl(bucket.Domain, bucket.SubDomain, relativePath, fileName);
                        //await InsertOrUpdateFileUrl(bucketFile.Id, computeUrl, providerCode);

                        Logger.LogDebug("Created new BucketFile: {FileName} in path: {RelativePath}",
                            fileName, relativePath);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing file: {FileName} in bucket: {BucketName}",
                    s3Object.Key, bucketName);
                // Continue processing other files
            }
        }

        private async Task InsertFileUrl(int fileId, string computeUrl, string providerCode)
        {
            var bucketFileUrl = new BucketFileUrl
            {
                BucketFileId = fileId,
                ComputeUrl = computeUrl,
                ProviderCode = providerCode
            };
            await _bucketFileUrlRepository.InsertAsync(bucketFileUrl, true);
        }

        private async Task InsertOrUpdateFileUrl(int fileId, string computeUrl, string providerCode)
        {
            var existingUrl = await _bucketFileUrlRepository.FirstOrDefaultAsync(u =>
                            u.BucketFileId == fileId && u.ProviderCode == ProviderCodeConstants.CloudFlare);

            if (existingUrl != null)
            {
                existingUrl.ComputeUrl = computeUrl;
                existingUrl.ProviderCode = providerCode;
                await _bucketFileUrlRepository.UpdateAsync(existingUrl, true);
            }
            else
            {
                await InsertFileUrl(fileId, computeUrl, providerCode);
            }
        }

        /// <summary>
        /// Extracts the file name and relative path from CloudFlare S3 object key
        /// </summary>
        /// <param name="s3ObjectKey">The S3 object key (full path)</param>
        /// <returns>Tuple containing (fileName, relativePath)</returns>
        private static (string fileName, string relativePath) ExtractFileNameAndPath(string s3ObjectKey)
        {
            if (string.IsNullOrWhiteSpace(s3ObjectKey))
            {
                return (string.Empty, string.Empty);
            }

            // Normalize path separators to forward slashes (CloudFlare/S3 standard)
            var normalizedKey = s3ObjectKey.Replace("\\", "/");

            // Remove leading slash if present
            if (normalizedKey.StartsWith('/'))
            {
                normalizedKey = normalizedKey.Substring(1);
            }

            // Extract file name (everything after the last slash)
            var lastSlashIndex = normalizedKey.LastIndexOf('/');
            string fileName;
            string relativePath;

            if (lastSlashIndex == -1)
            {
                // No folder structure, file is in root
                fileName = normalizedKey;
                relativePath = string.Empty;
            }
            else
            {
                // File is in a folder structure
                fileName = normalizedKey.Substring(lastSlashIndex + 1);
                relativePath = normalizedKey.Substring(0, lastSlashIndex);
            }

            // Ensure we have a valid file name
            if (string.IsNullOrWhiteSpace(fileName))
            {
                throw new InvalidOperationException($"Invalid S3 object key: '{s3ObjectKey}' - could not extract file name");
            }

            return (fileName, relativePath);
        }

        public static string CombineUrl(string baseUrl, string subdomain, string relativePath, string fileName)
        {
            baseUrl = $"https://{subdomain}.{baseUrl}";

            relativePath = relativePath.TrimStart('/').TrimEnd('/');
            fileName = fileName.TrimStart('/').TrimEnd('/');
            return $"{baseUrl}/{relativePath}/{fileName}";
        }

        // <summary>
        /// Updates or inserts BucketFileUrl records for all BucketFile records with 'cf' provider.
        /// Computes the file URL using the bucket's domain and subdomain configuration.
        /// </summary>
        /// <returns>Number of files processed</returns>
        [HttpPost]
        public async Task<int> UpdateFileUrlsAsync()
        {
            var filesProcessed = 0;

            try
            {
                Logger.LogInformation("Starting UpdateFileUrlAsync operation");

                // Get all CloudFlare storage buckets with their provider information
                var cfBuckets = await GetCloudFlareStorageBucketsAsync();
                if (cfBuckets.Count == 0)
                {
                    Logger.LogWarning("No CloudFlare storage buckets found for environment: {Environment}", _settings.Environment);
                    return 0;
                }

                var bucketMetaLookup = new Dictionary<string, BucketStorageMeta>();
                foreach (var cfBucket in cfBuckets)
                {
                    if (bucketMetaLookup.ContainsKey(cfBucket.BucketName))
                    {
                        continue; // Skip duplicates
                    }
                    bucketMetaLookup.Add(cfBucket.BucketName, new BucketStorageMeta
                    {
                        BucketName = cfBucket.BucketName,
                        LanguageCode = cfBucket.LanguageCode,
                        SpokenLangCode = cfBucket.SpokenLangCode,
                        ContentType = cfBucket.ContentType,
                        SubDomain = cfBucket.SubDomain,
                        Domain = cfBucket.StorageProvider.BindedDomain ?? string.Empty
                    });
                }

                // Process files in batches to avoid memory issues
                const int batchSize = 100;
                int offset = 0;
                int batchFilesProcessed;

                do
                {
                    using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);

                    // Get batch of files WITHIN the transaction scope to ensure consistency
                    var queryable = await _bucketFileRepository.GetQueryableAsync();
                    var batch = await queryable
                        .Where(f => f.Environment == _settings.Environment)
                        .OrderBy(f => f.Id) // Ensure consistent ordering
                        .Skip(offset)
                        .Take(batchSize)
                        .ToListAsync();

                    batchFilesProcessed = 0;

                    foreach (var bucketFile in batch)
                    {
                        var processed = await ProcessFileUrlUpdate(bucketFile, bucketMetaLookup);
                        if (processed)
                        {
                            filesProcessed++;
                            batchFilesProcessed++;
                        }
                    }

                    await uow.SaveChangesAsync();
                    await uow.CompleteAsync();

                    offset += batchSize;

                    Logger.LogDebug("Processed batch starting at offset {Offset}, files processed in batch: {BatchFilesProcessed}",
                        offset - batchSize, batchFilesProcessed);
                } while (batchFilesProcessed == batchSize); // Continue until we get a partial batch or empty batch

                Logger.LogInformation("UpdateFileUrlAsync completed. Total files processed: {TotalFiles}", filesProcessed);
                return filesProcessed;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error in UpdateFileUrlAsync operation");
                throw;
            }
        }

        /// <summary>
        /// Processes a single BucketFile to update or insert its BucketFileUrl record for CloudFlare provider.
        /// </summary>
        /// <param name="bucketFile">The BucketFile to process</param>
        /// <param name="bucketMetaLookup">Lookup dictionary for bucket metadata</param>
        /// <returns>True if file was processed successfully, false if skipped</returns>
        private async Task<bool> ProcessFileUrlUpdate(BucketFile bucketFile, Dictionary<string, BucketStorageMeta> bucketMetaLookup)
        {
            try
            {
                // Find the bucket metadata for this file
                if (!bucketMetaLookup.TryGetValue(bucketFile.BucketName, out var bucketMeta))
                {
                    Logger.LogWarning("No bucket metadata found for bucket: {BucketName}, file: {FileName}",
                        bucketFile.BucketName, bucketFile.FileName);
                    return false;
                }

                // Compute the file URL
                var computeUrl = CombineUrl(bucketMeta.Domain, bucketMeta.SubDomain,
                    bucketFile.RelativePathInBucket, bucketFile.FileName);

                var existingUrlData = await _bucketFileUrlRepository
                    .FirstOrDefaultAsync(u => u.BucketFileId == bucketFile.Id && u.ProviderCode == ProviderCodeConstants.CloudFlare)
                    ;

                if (existingUrlData != null)
                {
                    // Update existing record - only update if the URL has actually changed
                    if (existingUrlData.ComputeUrl != computeUrl)
                    {
                        existingUrlData.ComputeUrl = computeUrl;
                        await _bucketFileUrlRepository.UpdateAsync(existingUrlData, true);
                    }
                }
                else
                {
                    var bucketFileUrl = new BucketFileUrl
                    {
                        BucketFileId = bucketFile.Id,
                        ComputeUrl = computeUrl,
                        ProviderCode = ProviderCodeConstants.CloudFlare,
                    };
                    await _bucketFileUrlRepository.InsertAsync(bucketFileUrl, true);
                }

                return true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing file URL update for file: {FileName} (ID: {FileId})",
                    bucketFile.FileName, bucketFile.Id);
                return false; // Don't rethrow, continue processing other files
            }
        }

        /// <summary>
        /// Migrates CloudFlare bucket structure to VirtualDiskFolder table
        /// Creates root virtual folders for each bucket and walks through child paths
        /// </summary>
        /// <returns>Number of virtual folders created</returns>
        [HttpPost]
        public async Task<int> MigrateVirtualFoldersAsync()
        {
            var foldersCreated = 0;

            try
            {
                // Define the root virtual folders from the markdown specification
                var rootVirtualFolders = new[]
                {
                    new { Bucket = "notogaudio-cmn", Language = "", Variant = "cmn" },
                    new { Bucket = "notogvideo-zh-hant-yue", Language = "zh-Hant", Variant = "yue" },
                    new { Bucket = "notogaudio-yue", Language = "", Variant = "yue" },
                    new { Bucket = "docs-zh-hant", Language = "zh-Hant", Variant = "" },
                    new { Bucket = "docs-zh-hans", Language = "zh-Hans", Variant = "" },
                    new { Bucket = "ogvideo-zh-hans-cmn", Language = "zh-Hans", Variant = "cmn" },
                    new { Bucket = "notogvideo-en-eng", Language = "en", Variant = "eng" },
                    new { Bucket = "ogaudio-cmn", Language = "", Variant = "cmn" },
                    new { Bucket = "ogvideo-zh-hant-cmn", Language = "zh-Hant", Variant = "cmn" },
                    new { Bucket = "docs-en", Language = "en", Variant = "" },
                    new { Bucket = "notogvideo-zh-hans-cmn", Language = "zh-Hans", Variant = "cmn" }
                };

                foreach (var rootFolder in rootVirtualFolders)
                {
                    Logger.LogInformation("Processing bucket: {BucketName}", rootFolder.Bucket);

                    // Check if root folder already exists
                    var existingRootFolder = await _virtualFolderRepository.FirstOrDefaultAsync(
                        f => f.FolderName == rootFolder.Bucket && f.ParentFolderId == null);

                    VirtualDiskFolder rootVirtualFolder;
                    if (existingRootFolder == null)
                    {
                        // Create root virtual folder
                        rootVirtualFolder = new VirtualDiskFolder
                        {
                            FolderName = rootFolder.Bucket,
                            LanguageCode = string.IsNullOrEmpty(rootFolder.Language) ? "" : rootFolder.Language,
                            SpokenLangCode = string.IsNullOrEmpty(rootFolder.Variant) ? null : rootFolder.Variant,
                            ParentFolderId = null,
                            Views = 0,
                            Weight = 0
                        };

                        rootVirtualFolder = await _virtualFolderRepository.InsertAsync(rootVirtualFolder, autoSave: true);
                        foldersCreated++;
                        Logger.LogInformation("Created root virtual folder: {FolderName} (ID: {Id})",
                            rootVirtualFolder.FolderName, rootVirtualFolder.Id);
                    }
                    else
                    {
                        rootVirtualFolder = existingRootFolder;
                        Logger.LogInformation("Root virtual folder already exists: {FolderName} (ID: {Id})",
                            rootVirtualFolder.FolderName, rootVirtualFolder.Id);
                    }

                    // Process child folders and files for this bucket
                    var childFoldersCreated = await ProcessBucketChildrenAsync(rootFolder.Bucket, rootVirtualFolder.Id, rootFolder.Language, rootFolder.Variant);
                    foldersCreated += childFoldersCreated;
                }

                Logger.LogInformation("Migration completed. Total virtual folders created: {FoldersCreated}", foldersCreated);
                return foldersCreated;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error during virtual folder migration");
                throw;
            }
        }

        /// <summary>
        /// Processes child folders and files for a specific bucket
        /// </summary>
        /// <param name="bucketName">Name of the bucket to process</param>
        /// <param name="rootFolderId">ID of the root virtual folder</param>
        /// <returns>Number of child folders created</returns>
        private async Task<int> ProcessBucketChildrenAsync(string bucketName, int rootFolderId, string langCode, string? spokenLangCode)
        {
            var foldersCreated = 0;

            try
            {
                // Get all files for this bucket
                var bucketFiles = await _bucketFileRepository.GetQueryableAsync();
                var filesInBucket = await bucketFiles
                    .Where(f => f.BucketName == bucketName && f.Exists)
                    .ToListAsync();

                Logger.LogInformation("Found {FileCount} files in bucket: {BucketName}",
                    filesInBucket.Count, bucketName);

                // Group files by their directory path
                var filesByPath = filesInBucket
                    .Where(f => !string.IsNullOrEmpty(f.RelativePathInBucket))
                    .GroupBy(f => GetDirectoryPath(f.RelativePathInBucket))
                    .Where(g => !string.IsNullOrEmpty(g.Key) &&
                               !g.Key.Contains(".recycle") &&
                               !g.Key.Equals("/", StringComparison.OrdinalIgnoreCase))
                    .ToList();

                // Create virtual folders for each unique path
                foreach (var pathGroup in filesByPath)
                {
                    var folderPath = pathGroup.Key;
                    var pathParts = folderPath.Split('/', StringSplitOptions.RemoveEmptyEntries);

                    var currentParentId = rootFolderId;

                    // Create nested folder structure
                    for (int i = 0; i < pathParts.Length; i++)
                    {
                        var folderName = pathParts[i];
                        var currentPath = string.Join("/", pathParts.Take(i + 1));

                        // Check if folder already exists
                        var existingFolder = await _virtualFolderRepository.FirstOrDefaultAsync(
                            f => f.FolderName == folderName && f.ParentFolderId == currentParentId);

                        if (existingFolder == null)
                        {
                            // Create new folder
                            var newFolder = new VirtualDiskFolder
                            {
                                FolderName = folderName,
                                ParentFolderId = currentParentId,
                                LanguageCode = langCode,
                                SpokenLangCode = string.IsNullOrEmpty(spokenLangCode) ? null : spokenLangCode,
                                Views = 0,
                                Weight = 0
                            };

                            newFolder = await _virtualFolderRepository.InsertAsync(newFolder, autoSave: true);
                            currentParentId = newFolder.Id;
                            foldersCreated++;

                            Logger.LogDebug("Created virtual folder: {FolderName} (ID: {Id}) under parent: {ParentId}",
                                folderName, newFolder.Id, currentParentId);
                        }
                        else
                        {
                            currentParentId = existingFolder.Id;
                        }
                    }

                    // Add files to the deepest folder
                    if (currentParentId != rootFolderId)
                    {
                        await AddFilesToFolderAsync(pathGroup.ToList(), currentParentId);
                    }
                }

                // Add files that are directly in the root of the bucket
                var rootFiles = filesInBucket
                    .Where(f => string.IsNullOrEmpty(f.RelativePathInBucket) ||
                               f.RelativePathInBucket == "/" ||
                               !f.RelativePathInBucket.Contains('/'))
                    .Where(f => !f.FileName.Equals("index.html", StringComparison.OrdinalIgnoreCase))
                    .ToList();

                if (rootFiles.Any())
                {
                    await AddFilesToFolderAsync(rootFiles, rootFolderId);
                }

                return foldersCreated;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing bucket children for bucket: {BucketName}", bucketName);
                throw;
            }
        }

        /// <summary>
        /// Extracts the directory path from a file's relative path
        /// </summary>
        /// <param name="relativePath">The relative path of the file</param>
        /// <returns>The directory path</returns>
        private static string GetDirectoryPath(string relativePath)
        {
            if (string.IsNullOrEmpty(relativePath))
                return string.Empty;

            var lastSlashIndex = relativePath.LastIndexOf('/');
            if (lastSlashIndex <= 0)
                return string.Empty;

            return relativePath.Substring(0, lastSlashIndex);
        }

        /// <summary>
        /// Adds files to a virtual folder via FolderToFile relationship
        /// </summary>
        /// <param name="files">List of bucket files to add</param>
        /// <param name="folderId">ID of the virtual folder</param>
        /// <returns>Task</returns>
        private async Task AddFilesToFolderAsync(List<BucketFile> files, int folderId)
        {
            try
            {
                foreach (var file in files)
                {
                    // Check if relationship already exists
                    var existingRelation = await _folderToFileRepository.FirstOrDefaultAsync(
                        r => r.FolderId == folderId && r.BucketFileId == file.Id);

                    if (existingRelation == null)
                    {
                        var folderToFile = new FolderToFile
                        {
                            FolderId = folderId,
                            BucketFileId = file.Id,
                            Title = file.FileName, // Use filename as default title
                            IsDeleted = false
                        };

                        await _folderToFileRepository.InsertAsync(folderToFile, autoSave: true);

                        Logger.LogDebug("Added file {FileName} (ID: {FileId}) to folder {FolderId}",
                            file.FileName, file.Id, folderId);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error adding files to folder {FolderId}", folderId);
                throw;
            }
        }
    }
}