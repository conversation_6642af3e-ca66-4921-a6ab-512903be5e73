﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Sockets;
using System.Threading.Tasks;
using Amazon.S3;
using Amazon.S3.Model;
using HolyBless.Configs;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.VirtualFolders;
using HolyBless.Enums;
using HolyBless.Helpers;
using HolyBless.Repositories;
using HolyBless.StorageProviders.Dtos;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace HolyBless.StorageProviders
{
    public class S3StorageManager : ApplicationService, IS3StorageManager
    {
        private readonly IRepository<StorageBucket> _bucketRepository;
        private readonly IRepository<ProviderSecret> _providerSecretRepository;
        private readonly IBucketFileRepository _bucketFileRepository;
        private readonly IRepository<BucketFileUrl> _bucketFileUrlRepository;
        private readonly IRepository<VirtualDiskFolder> _virtualFolderRepository;
        private readonly IRepository<FolderToFile> _folderToFileRepository;
        private readonly IStorageProviderAppService _storageProviderAppService;
        private readonly IUnitOfWorkManager _unitOfWorkManager;
        private readonly AppConfig _settings;

        public S3StorageManager
            (
            IRepository<StorageBucket> bucketRepository
            , IRepository<ProviderSecret> providerSecretRepository
            , IBucketFileRepository bucketFileRepository
            , IRepository<BucketFileUrl> bucketFileUrlRepository
            , IRepository<VirtualDiskFolder> virtualFolderRepository
            , IRepository<FolderToFile> folderToFileRepository
            , IStorageProviderAppService storageProviderAppService
            , IUnitOfWorkManager unitOfWorkManager
            , IOptions<AppConfig> settings
            )
        {
            _bucketRepository = bucketRepository;
            _providerSecretRepository = providerSecretRepository;
            _bucketFileRepository = bucketFileRepository;
            _bucketFileUrlRepository = bucketFileUrlRepository;
            _virtualFolderRepository = virtualFolderRepository;
            _folderToFileRepository = folderToFileRepository;
            _storageProviderAppService = storageProviderAppService;
            _unitOfWorkManager = unitOfWorkManager;
            _settings = settings.Value;
        }

        private async Task<List<string>> ListBucketsAsync(ProviderSecret providerSecret)
        {
            return await ListBucketsAsync(providerSecret.AccessId, providerSecret.AccessSecretKey, providerSecret.ApiEndPoint);
        }

        private async Task<List<string>> ListBucketsAsync(string accessId, string accessSecretKey, string apiEndPoint)
        {
            var bucketNames = new List<string>();
            try
            {
                using var s3Client = GetS3Client(accessId, accessSecretKey, apiEndPoint);
                var response = await s3Client.ListBucketsAsync();
                foreach (var bucket in response.Buckets)
                {
                    bucketNames.Add(bucket.BucketName);
                }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }

            return bucketNames;
        }

        private async Task<StorageBucket> GetBucketById(int bucketId)
        {
            var query = await _bucketRepository.GetQueryableAsync();
            var bucket = await query.Include(x => x.StorageProvider).FirstOrDefaultAsync(x => x.Id == bucketId);
            Check.NotNull(bucket, nameof(StorageBucket));
            return bucket;
        }

        private AmazonS3Client GetS3Client(string accessId, string accessSecretKey, string apiEndPoint)
        {
            var config = new AmazonS3Config
            {
                ServiceURL = apiEndPoint,
                ForcePathStyle = false // AliYun requires path-style addressing to false
            };

            if (apiEndPoint.Contains(".r2.", StringComparison.OrdinalIgnoreCase))
            {
                config.ForcePathStyle = true; //R2 requires path-style addressing to true
            }
            var s3Client = new AmazonS3Client(accessId, accessSecretKey, config);
            return s3Client;
        }

        [HttpGet]
        public async Task<List<string>> ListSubfoldersAsync(int bucketId, string prefix = "")
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            var subfolders = new List<string>();

            try
            {
                var bucket = await GetBucketById(bucketId);
                var provider = bucket.StorageProvider;
                var request = new ListObjectsV2Request
                {
                    BucketName = bucket.BucketName,
                    Prefix = prefix,         // Start from a given prefix
                    Delimiter = "/"          // Treat `/` as a folder separator
                };

                // These properties are no longer available in StorageProvider class
                // var s3Client = GetS3Client(provider.AccessId, provider.AccessSecretKey, provider.ApiEndPoint);
                // var response = await s3Client.ListObjectsV2Async(request);

                throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");

                // Fetch common prefixes (subfolders)
                // subfolders.AddRange(response.CommonPrefixes);

                // Optionally, print objects (files) at the current level
                // foreach (var obj in response.S3Objects)
                // {
                //     Console.WriteLine($"File: {obj.Key}");
                // }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }

            return subfolders;
        }

        [HttpGet]
        public async Task<List<string>> ListFilesAsync(int bucketId)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");
        }

        [HttpGet]
        [Route("DownloadBucketFile")]
        public async Task<byte[]> DownloadBucketFile(int bucketId, string fileNamePath)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            // MemoryStream memory = await DownloadFileAsync(bucketId, fileNamePath);
            // memory.Position = 0;

            // var contentType = "application/octet-stream"; // Change based on file type if needed
            // var fileName = Path.GetFileName(fileNamePath);
            // return new FileStreamResult(memory, contentType)
            // {
            //     FileDownloadName = fileName
            // };

            throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");
        }

        public async Task<bool> UploadFileAsync(int bucketId, string subFolder, string fileName, Stream fileStream)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            try
            {
                var bucket = await GetBucketById(bucketId);
                var provider = bucket.StorageProvider;
                // Combine subfolder and filename to create the object key
                var objectKey = $"{subFolder.TrimEnd('/')}/{Path.GetFileName(fileName)}";

                var request = new PutObjectRequest
                {
                    BucketName = bucket.BucketName,
                    Key = objectKey,
                    InputStream = fileStream,
                    ContentType = "application/octet-stream" // Set appropriate MIME type if known
                };

                // These properties are no longer available in StorageProvider class
                // var s3Client = GetS3Client(provider.AccessId, provider.AccessSecretKey, provider.ApiEndPoint);
                // var response = await s3Client.PutObjectAsync(request);

                throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");

                // if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                // {
                //     return true;
                // }
                // else
                // {
                //     Logger.LogError("Failed to upload file. StatusCode: {StatusCode}", response.HttpStatusCode);
                //     return false;
                // }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<MemoryStream> DownloadFileAsync(int bucketId, string fieKey)
        {
            // NOTE: This method will not work as AccessId, AccessSecretKey, and ApiEndPoint properties have been commented out in StorageProvider class
            try
            {
                var bucket = await GetBucketById(bucketId);
                var provider = bucket.StorageProvider;
                var request = new GetObjectRequest
                {
                    BucketName = bucket.BucketName,
                    Key = fieKey
                };

                // These properties are no longer available in StorageProvider class
                // var s3Client = GetS3Client(provider.AccessId, provider.AccessSecretKey, provider.ApiEndPoint);
                // using (var response = await s3Client.GetObjectAsync(request))
                // {
                //     var memoryStream = new MemoryStream();
                //     await response.ResponseStream.CopyToAsync(memoryStream);
                //     return memoryStream;
                // }

                throw new NotImplementedException("AccessId, AccessSecretKey, and ApiEndPoint properties are no longer available in StorageProvider class");
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }
        }

        /// <summary>
        /// Walks through all CloudFlare storage buckets and their files, updating the BucketFile table
        /// </summary>
        /// <returns>Number of files processed</returns>
        [HttpPost]
        public async Task<int> IndexCloudFlareFilesAsync()
        {
            var filesProcessed = 0;

            try
            {
                var processingStartTime = DateTime.UtcNow;
                await ResetExistFlag(); // Reset all existing files to not exist before processing

                // Get provider secret (assuming there's one secret for CloudFlare)
                var providerSecret = await GetProviderSecretAsync();
                var actualCfBuckets = await ListBucketsAsync(providerSecret);

                // Get all distinct StorageBuckets for CloudFlare providers matching current environment
                var cfBuckets = await GetCloudFlareStorageBucketsAsync();

                if (!cfBuckets.Any())
                {
                    Logger.LogWarning("No CloudFlare storage buckets found for environment: {Environment}", _settings.Environment);
                    return 0;
                }
                var distinctBuckets = cfBuckets.Where(x => actualCfBuckets.Contains(x.BucketName)).Select(x => new BucketStorageMeta
                {
                    BucketName = x.BucketName,
                    LanguageCode = x.LanguageCode,
                    SpokenLangCode = x.SpokenLangCode,
                    ContentType = x.ContentType,
                    SubDomain = x.SubDomain,
                    Domain = x.StorageProvider.BindedDomain ?? string.Empty,
                }).Distinct().ToList();

                // Process each bucket
                var groupedBuckets = distinctBuckets
                                   .GroupBy(bucket => bucket.BucketName)
                                   .ToList();
                foreach (var grp in groupedBuckets)
                {
                    using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);
                    var bucketName = grp.Key;
                    //Not sure why above distinct doesn't remove duplicate
                    var groupItems = new List<BucketStorageMeta> { grp.First() };
                    var bucketFilesProcessed = await ProcessBucketFilesAsync(bucketName, groupItems, providerSecret, ProviderCodeConstants.CloudFlare);
                    filesProcessed += bucketFilesProcessed;
                    await uow.SaveChangesAsync();
                    await uow.CompleteAsync();
                }
                //await UpdateNotExistFiles(processingStartTime);
                Logger.LogInformation("Total files processed: {TotalFiles}", filesProcessed);
                return filesProcessed;
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                throw;
            }
        }

        private async Task ResetExistFlag()
        {
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);
            // Reset all BucketFile Exists flag to false for the current environment
            await _bucketFileRepository.UpdateExistFlagAsync(false);
            await uow.CompleteAsync();
        }

        private async Task UpdateNotExistFiles(DateTime processTime)
        {
            var notExists = await _bucketFileRepository.GetListAsync(x => x.Environment == _settings.Environment &&
                    x.LastModificationTime < processTime);
            foreach (var file in notExists)
            {
                file.Exists = false;
            }
            await _bucketFileRepository.UpdateManyAsync(notExists);
        }

        /// <summary>
        /// Gets CloudFlare storage buckets using the optimized cached provider service.
        /// This method leverages StorageProviderAppService's GetCachedAllProviders for better performance.
        /// </summary>
        /// <returns>List of CloudFlare storage buckets with StorageProvider included</returns>
        private async Task<List<StorageBucket>> GetCloudFlareStorageBucketsAsync()
        {
            try
            {
                // Use the optimized cached bucket DTOs from StorageProviderAppService
                var bucketDtos = await _storageProviderAppService.GetCloudFlareBucketsAsync();

                if (!bucketDtos.Any())
                {
                    Logger.LogWarning("No CloudFlare buckets found via StorageProviderAppService for environment: {Environment}",
                        _settings.Environment);
                    return new List<StorageBucket>();
                }

                //Only check cloud flare bucket
                var bucketIds = bucketDtos.Select(dto => dto.Id).ToList();
                var query = await _bucketRepository.GetQueryableAsync();

                var buckets = await query
                    .Include(x => x.StorageProvider) // Required for CloudFlare operations
                    .Where(x => bucketIds.Contains(x.Id))
                    .OrderBy(x => x.BucketName) // Consistent ordering
                    .ToListAsync();

                Logger.LogDebug("Retrieved {BucketCount} CloudFlare buckets from repository for synchronization",
                    buckets.Count);

                return buckets;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error retrieving CloudFlare buckets for file synchronization");
                throw;
            }
        }

        private async Task<ProviderSecret> GetProviderSecretAsync()
        {
            // For now, get the first available provider secret
            // In a real scenario, you might want to link secrets to specific providers
            var secret = await _providerSecretRepository.FirstOrDefaultAsync();
            Check.NotNull(secret, nameof(ProviderSecret), "No provider secret found. Please configure CloudFlare credentials.");
            return secret;
        }

        private async Task<int> ProcessBucketFilesAsync(string bucketName, List<BucketStorageMeta> buckets, ProviderSecret secret, string providerCode)
        {
            var filesProcessed = 0;

            try
            {
                using var s3Client = GetS3Client(secret.AccessId, secret.AccessSecretKey, secret.ApiEndPoint);

                // List all objects in the bucket
                var request = new ListObjectsV2Request
                {
                    BucketName = bucketName,
                    MaxKeys = 1000 // Process in batches
                };

                ListObjectsV2Response response;
                do
                {
                    response = await s3Client.ListObjectsV2Async(request);

                    foreach (var s3Object in response.S3Objects)
                    {
                        // Skip folders (objects ending with /)
                        if (s3Object.Key.EndsWith('/'))
                            continue;

                        await ProcessSingleFileAsync(bucketName, buckets, s3Object, providerCode);
                        filesProcessed++;
                    }

                    // Set continuation token for next batch
                    request.ContinuationToken = response.NextContinuationToken;
                } while (response.IsTruncated);

                return filesProcessed;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing files for bucket: {BucketName}", bucketName);
                throw;
            }
        }

        private async Task ProcessSingleFileAsync(string bucketName, List<BucketStorageMeta> buckets, S3Object s3Object, string providerCode)
        {
            try
            {
                // Extract file name and relative path from CloudFlare S3 object key
                var (fileName, relativePath) = ExtractFileNameAndPath(s3Object.Key);
                if (s3Object.Key.StartsWith(".recycle") || s3Object.Key.EndsWith("index.html", StringComparison.InvariantCultureIgnoreCase))
                {
                    return;
                }
                foreach (var bucket in buckets)
                {
                    // Check if file already exists in database
                    var existingFiles = await _bucketFileRepository.GetListAsync(f =>
                        f.FileName == fileName &&
                        (f.RelativePathInBucket ?? "") == (relativePath ?? "") &&
                         (string.IsNullOrWhiteSpace(f.BucketName) && f.Size == s3Object.Size ||
                         !string.IsNullOrWhiteSpace(f.BucketName) && (f.BucketName ?? "") == (bucketName ?? "")) &&
                         f.Environment == _settings.Environment)
                        ;
                    var existingFile = existingFiles.OrderBy(x => x.Id).FirstOrDefault();
                    /*if (existingFiles.Count > 1)
                    {
                        await _bucketFileRepository.DeleteManyAsync(existingFiles.Skip(1), true); // Remove duplicates, keep the first one
                    }*/
                    if (existingFile != null)
                    {
                        // Update existing file with CloudFlare information
                        existingFile.Exists = true;
                        existingFile.LastModificationTime = s3Object.LastModified;

                        // Ensure FileName and RelativePathInBucket are correctly set from CloudFlare
                        existingFile.FileName = fileName;
                        existingFile.RelativePathInBucket = relativePath;
                        existingFile.LanguageCode = bucket.LanguageCode;
                        existingFile.SpokenLangCode = bucket.SpokenLangCode;
                        existingFile.ContentCategory = bucket.ContentType;
                        existingFile.Size = s3Object.Size;
                        existingFile.BucketName = bucketName;
                        existingFile.MediaType = MediaTypeHelper.GetMediaType(fileName);
                        await _bucketFileRepository.UpdateAsync(existingFile, true);

                        // Create or update BucketFileUrl record for CloudFlare
                        //var computeUrl = CombineUrl(bucket.Domain, bucket.SubDomain, relativePath, fileName);
                        //await InsertOrUpdateFileUrl(existingFile.Id, computeUrl, providerCode);

                        Logger.LogDebug("Updated existing BucketFile: {FileName} in path: {RelativePath}",
                            fileName, relativePath);
                    }
                    else
                    {
                        // Create new file record with properly extracted CloudFlare path information
                        var bucketFile = new BucketFile
                        {
                            FileName = fileName,
                            RelativePathInBucket = relativePath,
                            LanguageCode = bucket.LanguageCode,
                            SpokenLangCode = bucket.SpokenLangCode,
                            Environment = _settings.Environment,
                            Exists = true,
                            Size = s3Object.Size,
                            BucketName = bucketName,
                            MediaType = MediaTypeHelper.GetMediaType(fileName),
                            ContentCategory = bucket.ContentType,
                            Views = 0
                        };

                        // Set LastModificationTime from S3 object
                        bucketFile.LastModifiedAtStorage = s3Object.LastModified;

                        bucketFile = await _bucketFileRepository.InsertAsync(bucketFile, true);

                        // Create BucketFileUrl record for CloudFlare
                        //var computeUrl = CombineUrl(bucket.Domain, bucket.SubDomain, relativePath, fileName);
                        //await InsertOrUpdateFileUrl(bucketFile.Id, computeUrl, providerCode);

                        Logger.LogDebug("Created new BucketFile: {FileName} in path: {RelativePath}",
                            fileName, relativePath);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing file: {FileName} in bucket: {BucketName}",
                    s3Object.Key, bucketName);
                // Continue processing other files
            }
        }

        private async Task InsertFileUrl(int fileId, string computeUrl, string providerCode)
        {
            var bucketFileUrl = new BucketFileUrl
            {
                BucketFileId = fileId,
                ComputeUrl = computeUrl,
                ProviderCode = providerCode
            };
            await _bucketFileUrlRepository.InsertAsync(bucketFileUrl, true);
        }

        private async Task InsertOrUpdateFileUrl(int fileId, string computeUrl, string providerCode)
        {
            var existingUrl = await _bucketFileUrlRepository.FirstOrDefaultAsync(u =>
                            u.BucketFileId == fileId && u.ProviderCode == ProviderCodeConstants.CloudFlare);

            if (existingUrl != null)
            {
                existingUrl.ComputeUrl = computeUrl;
                existingUrl.ProviderCode = providerCode;
                await _bucketFileUrlRepository.UpdateAsync(existingUrl, true);
            }
            else
            {
                await InsertFileUrl(fileId, computeUrl, providerCode);
            }
        }

        /// <summary>
        /// Extracts the file name and relative path from CloudFlare S3 object key
        /// </summary>
        /// <param name="s3ObjectKey">The S3 object key (full path)</param>
        /// <returns>Tuple containing (fileName, relativePath)</returns>
        private static (string fileName, string relativePath) ExtractFileNameAndPath(string s3ObjectKey)
        {
            if (string.IsNullOrWhiteSpace(s3ObjectKey))
            {
                return (string.Empty, string.Empty);
            }

            // Normalize path separators to forward slashes (CloudFlare/S3 standard)
            var normalizedKey = s3ObjectKey.Replace("\\", "/");

            // Remove leading slash if present
            if (normalizedKey.StartsWith('/'))
            {
                normalizedKey = normalizedKey.Substring(1);
            }

            // Extract file name (everything after the last slash)
            var lastSlashIndex = normalizedKey.LastIndexOf('/');
            string fileName;
            string relativePath;

            if (lastSlashIndex == -1)
            {
                // No folder structure, file is in root
                fileName = normalizedKey;
                relativePath = string.Empty;
            }
            else
            {
                // File is in a folder structure
                fileName = normalizedKey.Substring(lastSlashIndex + 1);
                relativePath = normalizedKey.Substring(0, lastSlashIndex);
            }

            // Ensure we have a valid file name
            if (string.IsNullOrWhiteSpace(fileName))
            {
                throw new InvalidOperationException($"Invalid S3 object key: '{s3ObjectKey}' - could not extract file name");
            }

            return (fileName, relativePath);
        }

        public static string CombineUrl(string baseUrl, string subdomain, string relativePath, string fileName)
        {
            baseUrl = $"https://{subdomain}.{baseUrl}";

            relativePath = relativePath.TrimStart('/').TrimEnd('/');
            fileName = fileName.TrimStart('/').TrimEnd('/');
            return $"{baseUrl}/{relativePath}/{fileName}";
        }

        // <summary>
        /// Updates or inserts BucketFileUrl records for all BucketFile records with 'cf' provider.
        /// Computes the file URL using the bucket's domain and subdomain configuration.
        /// </summary>
        /// <returns>Number of files processed</returns>
        [HttpPost]
        public async Task<int> UpdateFileUrlsAsync()
        {
            var filesProcessed = 0;

            try
            {
                Logger.LogInformation("Starting UpdateFileUrlAsync operation");

                // Get all CloudFlare storage buckets with their provider information
                var cfBuckets = await GetCloudFlareStorageBucketsAsync();
                if (cfBuckets.Count == 0)
                {
                    Logger.LogWarning("No CloudFlare storage buckets found for environment: {Environment}", _settings.Environment);
                    return 0;
                }

                var bucketMetaLookup = new Dictionary<string, BucketStorageMeta>();
                foreach (var cfBucket in cfBuckets)
                {
                    if (bucketMetaLookup.ContainsKey(cfBucket.BucketName))
                    {
                        continue; // Skip duplicates
                    }
                    bucketMetaLookup.Add(cfBucket.BucketName, new BucketStorageMeta
                    {
                        BucketName = cfBucket.BucketName,
                        LanguageCode = cfBucket.LanguageCode,
                        SpokenLangCode = cfBucket.SpokenLangCode,
                        ContentType = cfBucket.ContentType,
                        SubDomain = cfBucket.SubDomain,
                        Domain = cfBucket.StorageProvider.BindedDomain ?? string.Empty
                    });
                }

                // Process files in batches to avoid memory issues
                const int batchSize = 100;
                int offset = 0;
                int batchFilesProcessed;

                do
                {
                    using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);

                    // Get batch of files WITHIN the transaction scope to ensure consistency
                    var queryable = await _bucketFileRepository.GetQueryableAsync();
                    var batch = await queryable
                        .Where(f => f.Environment == _settings.Environment)
                        .OrderBy(f => f.Id) // Ensure consistent ordering
                        .Skip(offset)
                        .Take(batchSize)
                        .ToListAsync();

                    batchFilesProcessed = 0;

                    foreach (var bucketFile in batch)
                    {
                        var processed = await ProcessFileUrlUpdate(bucketFile, bucketMetaLookup);
                        if (processed)
                        {
                            filesProcessed++;
                            batchFilesProcessed++;
                        }
                    }

                    await uow.SaveChangesAsync();
                    await uow.CompleteAsync();

                    offset += batchSize;

                    Logger.LogDebug("Processed batch starting at offset {Offset}, files processed in batch: {BatchFilesProcessed}",
                        offset - batchSize, batchFilesProcessed);
                } while (batchFilesProcessed == batchSize); // Continue until we get a partial batch or empty batch

                Logger.LogInformation("UpdateFileUrlAsync completed. Total files processed: {TotalFiles}", filesProcessed);
                return filesProcessed;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error in UpdateFileUrlAsync operation");
                throw;
            }
        }

        /// <summary>
        /// Processes a single BucketFile to update or insert its BucketFileUrl record for CloudFlare provider.
        /// </summary>
        /// <param name="bucketFile">The BucketFile to process</param>
        /// <param name="bucketMetaLookup">Lookup dictionary for bucket metadata</param>
        /// <returns>True if file was processed successfully, false if skipped</returns>
        private async Task<bool> ProcessFileUrlUpdate(BucketFile bucketFile, Dictionary<string, BucketStorageMeta> bucketMetaLookup)
        {
            try
            {
                // Find the bucket metadata for this file
                if (!bucketMetaLookup.TryGetValue(bucketFile.BucketName, out var bucketMeta))
                {
                    Logger.LogWarning("No bucket metadata found for bucket: {BucketName}, file: {FileName}",
                        bucketFile.BucketName, bucketFile.FileName);
                    return false;
                }

                // Compute the file URL
                var computeUrl = CombineUrl(bucketMeta.Domain, bucketMeta.SubDomain,
                    bucketFile.RelativePathInBucket, bucketFile.FileName);

                var existingUrlData = await _bucketFileUrlRepository
                    .FirstOrDefaultAsync(u => u.BucketFileId == bucketFile.Id && u.ProviderCode == ProviderCodeConstants.CloudFlare)
                    ;

                if (existingUrlData != null)
                {
                    // Update existing record - only update if the URL has actually changed
                    if (existingUrlData.ComputeUrl != computeUrl)
                    {
                        existingUrlData.ComputeUrl = computeUrl;
                        await _bucketFileUrlRepository.UpdateAsync(existingUrlData, true);
                    }
                }
                else
                {
                    var bucketFileUrl = new BucketFileUrl
                    {
                        BucketFileId = bucketFile.Id,
                        ComputeUrl = computeUrl,
                        ProviderCode = ProviderCodeConstants.CloudFlare,
                    };
                    await _bucketFileUrlRepository.InsertAsync(bucketFileUrl, true);
                }

                return true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing file URL update for file: {FileName} (ID: {FileId})",
                    bucketFile.FileName, bucketFile.Id);
                return false; // Don't rethrow, continue processing other files
            }
        }

        /// <summary>
        /// Migrates CloudFlare bucket structure to VirtualDiskFolder table
        /// Creates root virtual folders for each bucket and walks through child paths
        /// </summary>
        /// <returns>Number of virtual folders created</returns>
        [HttpPost]
        public async Task<int> MigrateVirtualFoldersAsync()
        {
            var foldersCreated = 0;

            try
            {
                Logger.LogInformation("Starting VirtualFolder migration from CloudFlare buckets");

                // Get provider secret (same as IndexCloudFlareFilesAsync)
                var providerSecret = await GetProviderSecretAsync();
                var actualCfBuckets = await ListBucketsAsync(providerSecret);

                // Get all distinct StorageBuckets for CloudFlare providers matching current environment
                var cfBuckets = await GetCloudFlareStorageBucketsAsync();

                if (!cfBuckets.Any())
                {
                    Logger.LogWarning("No CloudFlare storage buckets found for environment: {Environment}", _settings.Environment);
                    return 0;
                }

                var distinctBuckets = cfBuckets.Where(x => actualCfBuckets.Contains(x.BucketName)).Select(x => new BucketStorageMeta
                {
                    BucketName = x.BucketName,
                    LanguageCode = x.LanguageCode,
                    SpokenLangCode = x.SpokenLangCode,
                    SubDomain = x.SubDomain,
                    Domain = x.StorageProvider.BindedDomain,
                    ContentType = x.ContentType
                }).ToList();

                Logger.LogInformation("Found {BucketCount} CloudFlare buckets to process", distinctBuckets.Count);

                // Process each bucket
                foreach (var bucketMeta in distinctBuckets)
                {
                    Logger.LogInformation("Processing bucket: {BucketName}", bucketMeta.BucketName);

                    // Create or get root virtual folder for this bucket
                    var rootFolder = await CreateOrGetRootVirtualFolderAsync(bucketMeta);
                    if (rootFolder.isNew)
                    {
                        foldersCreated++;
                    }

                    // Walk through CloudFlare bucket and create folder hierarchy
                    var bucketFoldersCreated = await ProcessCloudFlareBucketForVirtualFoldersAsync(
                        bucketMeta.BucketName, rootFolder.folder, providerSecret);
                    foldersCreated += bucketFoldersCreated;
                }

                Logger.LogInformation("Migration completed. Total virtual folders created: {FoldersCreated}", foldersCreated);
                return foldersCreated;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error during virtual folder migration");
                throw;
            }
        }

        /// <summary>
        /// Creates or gets the root virtual folder for a bucket
        /// </summary>
        /// <param name="bucketMeta">Bucket metadata</param>
        /// <returns>Tuple of folder and whether it was newly created</returns>
        private async Task<(VirtualDiskFolder folder, bool isNew)> CreateOrGetRootVirtualFolderAsync(BucketStorageMeta bucketMeta)
        {
            // Check if root folder already exists
            var existingRootFolder = await _virtualFolderRepository.FirstOrDefaultAsync(
                f => f.FolderName == bucketMeta.BucketName && f.ParentFolderId == null);

            if (existingRootFolder != null)
            {
                Logger.LogInformation("Root virtual folder already exists: {FolderName} (ID: {Id})",
                    existingRootFolder.FolderName, existingRootFolder.Id);
                return (existingRootFolder, false);
            }

            // Create root virtual folder
            var rootVirtualFolder = new VirtualDiskFolder
            {
                FolderName = bucketMeta.BucketName,
                LanguageCode = string.IsNullOrEmpty(bucketMeta.LanguageCode) ? "zh-Hans" : bucketMeta.LanguageCode,
                ParentFolderId = null,
                Views = 0,
                Weight = 0
            };

            rootVirtualFolder = await _virtualFolderRepository.InsertAsync(rootVirtualFolder, autoSave: true);
            Logger.LogInformation("Created root virtual folder: {FolderName} (ID: {Id})",
                rootVirtualFolder.FolderName, rootVirtualFolder.Id);

            return (rootVirtualFolder, true);
        }

        /// <summary>
        /// Processes CloudFlare bucket files and creates virtual folder hierarchy
        /// </summary>
        /// <param name="bucketName">Name of the bucket to process</param>
        /// <param name="rootFolder">Root virtual folder for this bucket</param>
        /// <param name="providerSecret">CloudFlare provider secret</param>
        /// <returns>Number of child folders created</returns>
        private async Task<int> ProcessCloudFlareBucketForVirtualFoldersAsync(string bucketName, VirtualDiskFolder rootFolder, ProviderSecret providerSecret)
        {
            var foldersCreated = 0;

            try
            {
                using var s3Client = GetS3Client(providerSecret.AccessId, providerSecret.AccessSecretKey, providerSecret.ApiEndPoint);

                // List all objects in the bucket (same pattern as IndexCloudFlareFilesAsync)
                var request = new ListObjectsV2Request
                {
                    BucketName = bucketName,
                    MaxKeys = 1000 // Process in batches
                };

                ListObjectsV2Response response;
                do
                {
                    response = await s3Client.ListObjectsV2Async(request);

                    foreach (var s3Object in response.S3Objects)
                    {
                        // Skip folders (objects ending with /)
                        if (s3Object.Key.EndsWith('/'))
                            continue;

                        // Skip .recycle folder and index.html files as specified
                        if (s3Object.Key.StartsWith(".recycle") ||
                            s3Object.Key.EndsWith("index.html", StringComparison.InvariantCultureIgnoreCase))
                            continue;

                        var childFoldersCreated = await ProcessSingleFileForVirtualFoldersAsync(bucketName, rootFolder, s3Object);
                        foldersCreated += childFoldersCreated;
                    }

                    // Set continuation token for next batch
                    request.ContinuationToken = response.NextContinuationToken;
                } while (response.IsTruncated);

                return foldersCreated;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing CloudFlare bucket for virtual folders: {BucketName}", bucketName);
                throw;
            }
        }

        /// <summary>
        /// Processes a single file from CloudFlare and creates virtual folder hierarchy
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="rootFolder">Root virtual folder</param>
        /// <param name="s3Object">S3 object from CloudFlare</param>
        /// <returns>Number of folders created</returns>
        private async Task<int> ProcessSingleFileForVirtualFoldersAsync(string bucketName, VirtualDiskFolder rootFolder, S3Object s3Object)
        {
            var foldersCreated = 0;

            try
            {
                // Extract file name and relative path from CloudFlare S3 object key
                var (fileName, relativePath) = ExtractFileNameAndPath(s3Object.Key);

                // Find the BucketFile record by bucket name and relative path
                var bucketFile = await FindBucketFileAsync(bucketName, fileName, relativePath);
                if (bucketFile == null)
                {
                    Logger.LogWarning("BucketFile not found for bucket: {BucketName}, file: {FileName}, path: {RelativePath}",
                        bucketName, fileName, relativePath);
                    return 0;
                }

                // Determine the target folder for this file
                VirtualDiskFolder targetFolder;
                if (string.IsNullOrEmpty(relativePath))
                {
                    // File is in root of bucket
                    targetFolder = rootFolder;
                }
                else
                {
                    // File is in a subfolder - create folder hierarchy
                    var folderResult = await CreateFolderHierarchyAsync(relativePath, rootFolder);
                    targetFolder = folderResult.folder;
                    foldersCreated += folderResult.foldersCreated;
                }

                // Create FolderToFile relationship
                await CreateFolderToFileRelationshipAsync(targetFolder.Id, bucketFile.Id, fileName);

                return foldersCreated;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing single file for virtual folders: {S3Key}", s3Object.Key);
                throw;
            }
        }

        /// <summary>
        /// Finds a BucketFile record by bucket name, file name, and relative path
        /// </summary>
        /// <param name="bucketName">Name of the bucket</param>
        /// <param name="fileName">Name of the file</param>
        /// <param name="relativePath">Relative path in bucket</param>
        /// <returns>BucketFile if found, null otherwise</returns>
        private async Task<BucketFile?> FindBucketFileAsync(string bucketName, string fileName, string relativePath)
        {
            try
            {
                var queryable = await _bucketFileRepository.GetQueryableAsync();

                // Look up file by bucket name, file name, and relative path
                var bucketFile = await queryable
                    .Where(f => f.BucketName == bucketName &&
                               f.FileName == fileName &&
                               f.RelativePathInBucket == relativePath &&
                               f.Exists)
                    .FirstOrDefaultAsync();

                return bucketFile;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error finding BucketFile for bucket: {BucketName}, file: {FileName}, path: {RelativePath}",
                    bucketName, fileName, relativePath);
                return null;
            }
        }

        /// <summary>
        /// Creates folder hierarchy for a given path
        /// </summary>
        /// <param name="relativePath">Relative path to create folders for</param>
        /// <param name="rootFolder">Root folder to start from</param>
        /// <returns>Tuple of target folder and number of folders created</returns>
        private async Task<(VirtualDiskFolder folder, int foldersCreated)> CreateFolderHierarchyAsync(string relativePath, VirtualDiskFolder rootFolder)
        {
            var foldersCreated = 0;
            var currentFolder = rootFolder;

            try
            {
                var pathParts = relativePath.Split('/', StringSplitOptions.RemoveEmptyEntries);

                foreach (var folderName in pathParts)
                {
                    // Check if folder already exists
                    var existingFolder = await _virtualFolderRepository.FirstOrDefaultAsync(
                        f => f.FolderName == folderName && f.ParentFolderId == currentFolder.Id);

                    if (existingFolder == null)
                    {
                        // Create new folder
                        var newFolder = new VirtualDiskFolder
                        {
                            FolderName = folderName,
                            ParentFolderId = currentFolder.Id,
                            LanguageCode = currentFolder.LanguageCode, // Inherit from parent
                            Views = 0,
                            Weight = 0
                        };

                        newFolder = await _virtualFolderRepository.InsertAsync(newFolder, autoSave: true);
                        currentFolder = newFolder;
                        foldersCreated++;

                        Logger.LogDebug("Created virtual folder: {FolderName} (ID: {Id}) under parent: {ParentId}",
                            folderName, newFolder.Id, newFolder.ParentFolderId);
                    }
                    else
                    {
                        currentFolder = existingFolder;
                    }
                }

                return (currentFolder, foldersCreated);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error creating folder hierarchy for path: {RelativePath}", relativePath);
                throw;
            }
        }

        /// <summary>
        /// Creates a FolderToFile relationship between a virtual folder and a bucket file
        /// </summary>
        /// <param name="folderId">ID of the virtual folder</param>
        /// <param name="bucketFileId">ID of the bucket file</param>
        /// <param name="fileName">Name of the file (used as title)</param>
        /// <returns>Task</returns>
        private async Task CreateFolderToFileRelationshipAsync(int folderId, int bucketFileId, string fileName)
        {
            try
            {
                // Check if relationship already exists
                var existingRelation = await _folderToFileRepository.FirstOrDefaultAsync(
                    r => r.FolderId == folderId && r.BucketFileId == bucketFileId);

                if (existingRelation == null)
                {
                    var folderToFile = new FolderToFile
                    {
                        FolderId = folderId,
                        BucketFileId = bucketFileId,
                        Title = fileName, // Use filename as default title
                        IsDeleted = false
                    };

                    await _folderToFileRepository.InsertAsync(folderToFile, autoSave: true);

                    Logger.LogDebug("Created FolderToFile relationship: File {FileName} (ID: {FileId}) to folder {FolderId}",
                        fileName, bucketFileId, folderId);
                }
                else
                {
                    Logger.LogDebug("FolderToFile relationship already exists: File {FileName} (ID: {FileId}) to folder {FolderId}",
                        fileName, bucketFileId, folderId);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error creating FolderToFile relationship for file {FileName} (ID: {FileId}) to folder {FolderId}",
                    fileName, bucketFileId, folderId);
                throw;
            }
        }
    }
}