﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HolyBless.Migrations
{
    /// <inheritdoc />
    public partial class RenametoFolderToFiles : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FolderToBucketFiles_BucketFiles_BucketFileId",
                table: "FolderToBucketFiles");

            migrationBuilder.DropForeignKey(
                name: "FK_FolderToBucketFiles_VirtualDiskFolders_FolderId",
                table: "FolderToBucketFiles");

            migrationBuilder.DropPrimaryKey(
                name: "PK_FolderToBucketFiles",
                table: "FolderToBucketFiles");

            migrationBuilder.RenameTable(
                name: "FolderToBucketFiles",
                newName: "FolderToFiles");

            migrationBuilder.RenameIndex(
                name: "IX_FolderToBucketFiles_BucketFileId",
                table: "FolderToFiles",
                newName: "IX_FolderToFiles_BucketFileId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_FolderToFiles",
                table: "FolderToFiles",
                columns: new[] { "FolderId", "BucketFileId" });

            migrationBuilder.AddForeignKey(
                name: "FK_FolderToFiles_BucketFiles_BucketFileId",
                table: "FolderToFiles",
                column: "BucketFileId",
                principalTable: "BucketFiles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_FolderToFiles_VirtualDiskFolders_FolderId",
                table: "FolderToFiles",
                column: "FolderId",
                principalTable: "VirtualDiskFolders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FolderToFiles_BucketFiles_BucketFileId",
                table: "FolderToFiles");

            migrationBuilder.DropForeignKey(
                name: "FK_FolderToFiles_VirtualDiskFolders_FolderId",
                table: "FolderToFiles");

            migrationBuilder.DropPrimaryKey(
                name: "PK_FolderToFiles",
                table: "FolderToFiles");

            migrationBuilder.RenameTable(
                name: "FolderToFiles",
                newName: "FolderToBucketFiles");

            migrationBuilder.RenameIndex(
                name: "IX_FolderToFiles_BucketFileId",
                table: "FolderToBucketFiles",
                newName: "IX_FolderToBucketFiles_BucketFileId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_FolderToBucketFiles",
                table: "FolderToBucketFiles",
                columns: new[] { "FolderId", "BucketFileId" });

            migrationBuilder.AddForeignKey(
                name: "FK_FolderToBucketFiles_BucketFiles_BucketFileId",
                table: "FolderToBucketFiles",
                column: "BucketFileId",
                principalTable: "BucketFiles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_FolderToBucketFiles_VirtualDiskFolders_FolderId",
                table: "FolderToBucketFiles",
                column: "FolderId",
                principalTable: "VirtualDiskFolders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
