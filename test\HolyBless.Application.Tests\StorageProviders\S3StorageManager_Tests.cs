using System;
using Xunit;
using System.Reflection;
using System.Threading.Tasks;
using System.Linq;
using HolyBless.Entities.VirtualFolders;
using HolyBless.Entities.Buckets;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;
using Shouldly;

namespace HolyBless.StorageProviders
{
    public class S3StorageManager_Tests
    {
        [Theory]
        [InlineData("image.jpg", "image.jpg", "")]
        [InlineData("folder/image.jpg", "image.jpg", "folder")]
        [InlineData("folder/subfolder/image.jpg", "image.jpg", "folder/subfolder")]
        [InlineData("deep/nested/folder/structure/document.pdf", "document.pdf", "deep/nested/folder/structure")]
        [InlineData("/leading/slash/file.txt", "file.txt", "leading/slash")]
        [InlineData("folder\\with\\backslashes\\file.doc", "file.doc", "folder/with/backslashes")]
        [InlineData("mixed/folder\\structure/file.png", "file.png", "mixed/folder/structure")]
        [InlineData("chinese/中文文件夹/测试.txt", "测试.txt", "chinese/中文文件夹")]
        [InlineData("spaces in folder/file with spaces.pdf", "file with spaces.pdf", "spaces in folder")]
        public void ExtractFileNameAndPath_ShouldHandleVariousCloudFlarePathStructures(
            string s3ObjectKey, 
            string expectedFileName, 
            string expectedRelativePath)
        {
            // Act
            var result = CallPrivateExtractFileNameAndPath(s3ObjectKey);

            // Assert
            Assert.Equal(expectedFileName, result.fileName);
            Assert.Equal(expectedRelativePath, result.relativePath);
        }

        [Theory]
        [InlineData("")]
        [InlineData("   ")]
        [InlineData(null)]
        public void ExtractFileNameAndPath_ShouldHandleEmptyOrNullInput(string s3ObjectKey)
        {
            // Act
            var result = CallPrivateExtractFileNameAndPath(s3ObjectKey ?? string.Empty);

            // Assert
            Assert.Equal(string.Empty, result.fileName);
            Assert.Equal(string.Empty, result.relativePath);
        }

        [Theory]
        [InlineData("folder/")]
        [InlineData("folder/subfolder/")]
        public void ExtractFileNameAndPath_ShouldThrowForInvalidKeys(string s3ObjectKey)
        {
            // Act & Assert
            var exception = Assert.Throws<TargetInvocationException>(() => CallPrivateExtractFileNameAndPath(s3ObjectKey));
            Assert.IsType<InvalidOperationException>(exception.InnerException);
        }

        /// <summary>
        /// Helper method to call the private ExtractFileNameAndPath method using reflection
        /// </summary>
        private static (string fileName, string relativePath) CallPrivateExtractFileNameAndPath(string s3ObjectKey)
        {
            var method = typeof(S3StorageManager).GetMethod("ExtractFileNameAndPath", 
                BindingFlags.NonPublic | BindingFlags.Static);
            
            if (method == null)
            {
                throw new InvalidOperationException("ExtractFileNameAndPath method not found");
            }

            var result = method.Invoke(null, new object[] { s3ObjectKey });
            return ((string, string))result;
        }
    }

    public abstract class S3StorageManager_Integration_Tests<TStartupModule> : HolyBlessApplicationTestBase<TStartupModule>
        where TStartupModule : IAbpModule
    {
        private readonly IS3StorageManager _s3StorageManager;
        private readonly IRepository<VirtualDiskFolder> _virtualFolderRepository;
        private readonly IRepository<FolderToFile> _folderToFileRepository;
        private readonly IRepository<BucketFile> _bucketFileRepository;

        protected S3StorageManager_Integration_Tests()
        {
            _s3StorageManager = GetRequiredService<IS3StorageManager>();
            _virtualFolderRepository = GetRequiredService<IRepository<VirtualDiskFolder>>();
            _folderToFileRepository = GetRequiredService<IRepository<FolderToFile>>();
            _bucketFileRepository = GetRequiredService<IRepository<BucketFile>>();
        }

        [Fact]
        public async Task MigrateVirtualFoldersAsync_Should_Create_Root_Virtual_Folders()
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange - Create some test bucket files
                await CreateTestBucketFilesAsync();

                // Act
                var result = await _s3StorageManager.MigrateVirtualFoldersAsync();

                // Assert
                result.ShouldBeGreaterThan(0);

                // Verify root folders were created
                var rootFolders = await _virtualFolderRepository.GetListAsync(f => f.ParentFolderId == null);
                rootFolders.ShouldNotBeEmpty();

                // Check for specific bucket folders
                var expectedBuckets = new[] { "docs-en", "docs-zh-hans", "docs-zh-hant" };
                foreach (var bucketName in expectedBuckets)
                {
                    var folder = rootFolders.FirstOrDefault(f => f.FolderName == bucketName);
                    folder.ShouldNotBeNull($"Root folder for bucket '{bucketName}' should be created");
                }
            });
        }

        [Fact]
        public async Task MigrateVirtualFoldersAsync_Should_Create_Child_Folders_And_File_Relationships()
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange - Create test bucket files with folder structure
                await CreateTestBucketFilesWithFoldersAsync();

                // Act
                var result = await _s3StorageManager.MigrateVirtualFoldersAsync();

                // Assert
                result.ShouldBeGreaterThan(0);

                // Verify child folders were created
                var allFolders = await _virtualFolderRepository.GetListAsync();
                var childFolders = allFolders.Where(f => f.ParentFolderId != null).ToList();
                childFolders.ShouldNotBeEmpty();

                // Verify file relationships were created
                var fileRelationships = await _folderToFileRepository.GetListAsync();
                fileRelationships.ShouldNotBeEmpty();
            });
        }

        [Fact]
        public async Task MigrateVirtualFoldersAsync_Should_Skip_Existing_Folders()
        {
            await WithUnitOfWorkAsync(async () =>
            {
                // Arrange - Create a root folder first
                var existingFolder = new VirtualDiskFolder
                {
                    FolderName = "docs-en",
                    LanguageCode = "en",
                    ParentFolderId = null,
                    Views = 0,
                    Weight = 0
                };
                await _virtualFolderRepository.InsertAsync(existingFolder, autoSave: true);

                var initialCount = await _virtualFolderRepository.GetCountAsync();

                // Act
                var result = await _s3StorageManager.MigrateVirtualFoldersAsync();

                // Assert
                var finalCount = await _virtualFolderRepository.GetCountAsync();

                // Should not duplicate the existing folder
                var docsEnFolders = await _virtualFolderRepository.GetListAsync(f => f.FolderName == "docs-en" && f.ParentFolderId == null);
                docsEnFolders.Count.ShouldBe(1);
            });
        }

        private async Task CreateTestBucketFilesAsync()
        {
            var testFiles = new[]
            {
                new BucketFile { FileName = "test1.pdf", BucketName = "docs-en", RelativePathInBucket = "", LanguageCode = "en", Exists = true },
                new BucketFile { FileName = "test2.pdf", BucketName = "docs-zh-hans", RelativePathInBucket = "", LanguageCode = "zh-Hans", Exists = true },
                new BucketFile { FileName = "test3.pdf", BucketName = "docs-zh-hant", RelativePathInBucket = "", LanguageCode = "zh-Hant", Exists = true }
            };

            foreach (var file in testFiles)
            {
                await _bucketFileRepository.InsertAsync(file, autoSave: true);
            }
        }

        private async Task CreateTestBucketFilesWithFoldersAsync()
        {
            var testFiles = new[]
            {
                new BucketFile { FileName = "doc1.pdf", BucketName = "docs-en", RelativePathInBucket = "folder1", LanguageCode = "en", Exists = true },
                new BucketFile { FileName = "doc2.pdf", BucketName = "docs-en", RelativePathInBucket = "folder1/subfolder", LanguageCode = "en", Exists = true },
                new BucketFile { FileName = "doc3.pdf", BucketName = "docs-zh-hans", RelativePathInBucket = "中文文件夹", LanguageCode = "zh-Hans", Exists = true },
                new BucketFile { FileName = "index.html", BucketName = "docs-en", RelativePathInBucket = "", LanguageCode = "en", Exists = true }, // Should be ignored
                new BucketFile { FileName = "recycle.txt", BucketName = "docs-en", RelativePathInBucket = ".recycle", LanguageCode = "en", Exists = true } // Should be ignored
            };

            foreach (var file in testFiles)
            {
                await _bucketFileRepository.InsertAsync(file, autoSave: true);
            }
        }
    }
}
