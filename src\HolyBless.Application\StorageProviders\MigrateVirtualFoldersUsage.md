# MigrateVirtualFolders Usage Guide

## Overview

The `MigrateVirtualFoldersAsync` method in `S3StorageManager` migrates CloudFlare bucket structure to the VirtualDiskFolder table. This method **directly walks through CloudFlare buckets** using the same pattern as `IndexCloudFlareFilesAsync()` and creates a hierarchical folder structure based on the actual files stored in CloudFlare.

## What it does

1. **Connects to CloudFlare**: Uses the same CloudFlare connection logic as `IndexCloudFlareFilesAsync()` to access actual bucket contents
2. **Creates Root Virtual Folders**: For each CloudFlare bucket found, creates a root VirtualDiskFolder entry
3. **Walks CloudFlare Files**: Iterates through all files in each CloudFlare bucket using S3 ListObjectsV2 API
4. **Creates Folder Hierarchy**: Analyzes each file's path and creates nested VirtualDiskFolder items as needed
5. **Links Files to Folders**: Looks up BucketFile records by bucket name and relative path, then creates FolderToFile relationships
6. **Ignores Unwanted Content**: Skips `.recycle` folders and `index.html` files as specified

## Supported CloudFlare Buckets

The method processes **all CloudFlare buckets** found in the current environment by:

1. **Discovering Buckets**: Uses `GetCloudFlareStorageBucketsAsync()` to find all configured CloudFlare buckets
2. **Verifying Access**: Cross-references with actual CloudFlare buckets using `ListBucketsAsync()`
3. **Processing All Found**: Creates virtual folders for every accessible CloudFlare bucket

This means the method is **dynamic** and will process any CloudFlare bucket configured in the system, not just a predefined list.

## Usage

### Via HTTP API

The method is exposed as an HTTP POST endpoint:

```http
POST /api/app/s3-storage-manager/migrate-virtual-folders
```

### Via Dependency Injection

```csharp
public class MyService
{
    private readonly IS3StorageManager _s3StorageManager;

    public MyService(IS3StorageManager s3StorageManager)
    {
        _s3StorageManager = s3StorageManager;
    }

    public async Task MigrateFolders()
    {
        var foldersCreated = await _s3StorageManager.MigrateVirtualFoldersAsync();
        Console.WriteLine($"Created {foldersCreated} virtual folders");
    }
}
```

## Example Folder Structure

Given bucket files like:
```
docs-en/
├── introduction.pdf
├── tutorials/
│   ├── getting-started.pdf
│   └── advanced/
│       └── configuration.pdf
└── api/
    └── reference.pdf
```

The method will create:
1. Root folder: `docs-en`
2. Child folders: `tutorials`, `advanced`, `api`
3. File relationships linking each PDF to its containing folder

## Return Value

Returns the number of virtual folders created during the migration process.

## Error Handling

- Logs detailed information about the migration process
- Skips existing folders (idempotent operation)
- Continues processing other buckets if one fails
- Throws exceptions for critical errors

## Prerequisites

- **CloudFlare Access**: Valid CloudFlare credentials configured in ProviderSecret table
- **CloudFlare Buckets**: CloudFlare buckets must be configured in StorageBucket table
- **BucketFile Records**: BucketFile records should exist (typically created by `IndexCloudFlareFilesAsync()`)
- **Database Access**: Application must have access to VirtualDiskFolder and FolderToFile repositories
- **Permissions**: Proper database permissions for creating new records

## Recommended Workflow

1. **First**: Run `IndexCloudFlareFilesAsync()` to populate BucketFile table with current CloudFlare contents
2. **Then**: Run `MigrateVirtualFoldersAsync()` to create virtual folder structure based on those files

## Notes

- The operation is idempotent - running it multiple times won't create duplicate folders
- Files without folder structure are linked directly to the root bucket folder
- The method respects the existing folder hierarchy and won't overwrite existing relationships
