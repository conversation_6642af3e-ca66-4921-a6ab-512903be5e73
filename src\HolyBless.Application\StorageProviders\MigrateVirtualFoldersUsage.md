# MigrateVirtualFolders Usage Guide

## Overview

The `MigrateVirtualFoldersAsync` method in `S3StorageManager` migrates CloudFlare bucket structure to the VirtualDiskFolder table. This method creates a hierarchical folder structure based on the files stored in CloudFlare buckets.

## What it does

1. **Creates Root Virtual Folders**: For each predefined CloudFlare bucket, creates a root VirtualDiskFolder entry
2. **Walks Child Paths**: Analyzes the file paths in each bucket and creates child VirtualDiskFolder items for each unique directory
3. **Links Files to Folders**: Adds entries to the FolderToFile table to associate files with their containing folders
4. **Ignores Unwanted Content**: Skips `.recycle` folders and `index.html` files

## Supported Root Buckets

The method processes these predefined CloudFlare buckets:

| Bucket Name | Language | Variant |
|-------------|----------|---------|
| notogaudio-cmn | | cmn |
| notogvideo-zh-hant-yue | zh-Hant | yue |
| notogaudio-yue | | yue |
| docs-zh-hant | zh-Hant | |
| docs-zh-hans | zh-Hans | |
| ogvideo-zh-hans-cmn | zh-Hans | cmn |
| notogvideo-en-eng | en | eng |
| ogaudio-cmn | | cmn |
| ogvideo-zh-hant-cmn | zh-Hant | cmn |
| docs-en | en | |
| notogvideo-zh-hans-cmn | zh-Hans | cmn |

## Usage

### Via HTTP API

The method is exposed as an HTTP POST endpoint:

```http
POST /api/app/s3-storage-manager/migrate-virtual-folders
```

### Via Dependency Injection

```csharp
public class MyService
{
    private readonly IS3StorageManager _s3StorageManager;

    public MyService(IS3StorageManager s3StorageManager)
    {
        _s3StorageManager = s3StorageManager;
    }

    public async Task MigrateFolders()
    {
        var foldersCreated = await _s3StorageManager.MigrateVirtualFoldersAsync();
        Console.WriteLine($"Created {foldersCreated} virtual folders");
    }
}
```

## Example Folder Structure

Given bucket files like:
```
docs-en/
├── introduction.pdf
├── tutorials/
│   ├── getting-started.pdf
│   └── advanced/
│       └── configuration.pdf
└── api/
    └── reference.pdf
```

The method will create:
1. Root folder: `docs-en`
2. Child folders: `tutorials`, `advanced`, `api`
3. File relationships linking each PDF to its containing folder

## Return Value

Returns the number of virtual folders created during the migration process.

## Error Handling

- Logs detailed information about the migration process
- Skips existing folders (idempotent operation)
- Continues processing other buckets if one fails
- Throws exceptions for critical errors

## Prerequisites

- BucketFile records must exist in the database with correct `BucketName` and `RelativePathInBucket` values
- The application must have access to VirtualDiskFolder and FolderToFile repositories
- Proper database permissions for creating new records

## Notes

- The operation is idempotent - running it multiple times won't create duplicate folders
- Files without folder structure are linked directly to the root bucket folder
- The method respects the existing folder hierarchy and won't overwrite existing relationships
