﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HolyBless.Migrations
{
    /// <inheritdoc />
    public partial class AddspokenVirtualFolderUrl512 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "SpokenLangCode",
                table: "VirtualDiskFolders",
                type: "character varying(10)",
                unicode: false,
                maxLength: 10,
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "SubDomain",
                table: "StorageBuckets",
                type: "character varying(512)",
                unicode: false,
                maxLength: 512,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(256)",
                oldUnicode: false,
                oldMaxLength: 256);

            migrationBuilder.AlterColumn<string>(
                name: "ApiEndPoint",
                table: "ProviderSecrets",
                type: "character varying(512)",
                unicode: false,
                maxLength: 512,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(256)",
                oldUnicode: false,
                oldMaxLength: 256);

            migrationBuilder.AlterColumn<string>(
                name: "AccessSecretKey",
                table: "ProviderSecrets",
                type: "character varying(512)",
                unicode: false,
                maxLength: 512,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(256)",
                oldUnicode: false,
                oldMaxLength: 256);

            migrationBuilder.AlterColumn<string>(
                name: "AccessId",
                table: "ProviderSecrets",
                type: "character varying(512)",
                unicode: false,
                maxLength: 512,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(256)",
                oldUnicode: false,
                oldMaxLength: 256);

            migrationBuilder.AlterColumn<string>(
                name: "ComputeUrl",
                table: "BucketFileUrls",
                type: "character varying(512)",
                unicode: false,
                maxLength: 512,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(256)",
                oldUnicode: false,
                oldMaxLength: 256);

            migrationBuilder.AlterColumn<string>(
                name: "YoutubeId",
                table: "BucketFiles",
                type: "character varying(512)",
                unicode: false,
                maxLength: 512,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(256)",
                oldUnicode: false,
                oldMaxLength: 256,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "RelativePathInBucket",
                table: "BucketFiles",
                type: "character varying(512)",
                maxLength: 512,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(256)",
                oldMaxLength: 256);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SpokenLangCode",
                table: "VirtualDiskFolders");

            migrationBuilder.AlterColumn<string>(
                name: "SubDomain",
                table: "StorageBuckets",
                type: "character varying(256)",
                unicode: false,
                maxLength: 256,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(512)",
                oldUnicode: false,
                oldMaxLength: 512);

            migrationBuilder.AlterColumn<string>(
                name: "ApiEndPoint",
                table: "ProviderSecrets",
                type: "character varying(256)",
                unicode: false,
                maxLength: 256,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(512)",
                oldUnicode: false,
                oldMaxLength: 512);

            migrationBuilder.AlterColumn<string>(
                name: "AccessSecretKey",
                table: "ProviderSecrets",
                type: "character varying(256)",
                unicode: false,
                maxLength: 256,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(512)",
                oldUnicode: false,
                oldMaxLength: 512);

            migrationBuilder.AlterColumn<string>(
                name: "AccessId",
                table: "ProviderSecrets",
                type: "character varying(256)",
                unicode: false,
                maxLength: 256,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(512)",
                oldUnicode: false,
                oldMaxLength: 512);

            migrationBuilder.AlterColumn<string>(
                name: "ComputeUrl",
                table: "BucketFileUrls",
                type: "character varying(256)",
                unicode: false,
                maxLength: 256,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(512)",
                oldUnicode: false,
                oldMaxLength: 512);

            migrationBuilder.AlterColumn<string>(
                name: "YoutubeId",
                table: "BucketFiles",
                type: "character varying(256)",
                unicode: false,
                maxLength: 256,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(512)",
                oldUnicode: false,
                oldMaxLength: 512,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "RelativePathInBucket",
                table: "BucketFiles",
                type: "character varying(256)",
                maxLength: 256,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(512)",
                oldMaxLength: 512);
        }
    }
}
