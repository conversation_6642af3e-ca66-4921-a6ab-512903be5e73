
## List of Root Virtual Folders

| Bucket | Language | Variant |
|--------|----------|---------|
|	notogaudio-cmn	|		|	cmn	|
|	notogvideo-zh-hant-yue	|	zh-Hant	|	yue	|
|	notogaudio-yue	|		|	yue	|
|	docs-zh-hant	|	zh-Han<PERSON>	|		|
|	docs-zh-hans	|	zh-<PERSON>	|		|
|	ogvideo-zh-hans-cmn	|	zh-Hans	|	cmn	|
|	notogvideo-en-eng	|	en	|	eng	|
|	ogaudio-cmn	|		|	cmn	|
|	ogvideo-zh-hant-cmn	|	zh-Hant	|	cmn	|
|	docs-en	|	en	|		|
|	notogvideo-zh-hans-cmn	|	zh-Hans	|	cmn	|

## Migrate to VirtualDiskFolder table

### Add method to S3StorageManager called MigrateVirtualFolders

* For each above CF bucket, create a root VirtualDiskFolder entry
* Walk through each child path of the bucket, create a child VirtualDiskFolder item
* For file object within a child path, add a entry to FolderToFile table, the fileId can be found via BucketFileUrl table
* Ignore '.recycle' folder and index.html file